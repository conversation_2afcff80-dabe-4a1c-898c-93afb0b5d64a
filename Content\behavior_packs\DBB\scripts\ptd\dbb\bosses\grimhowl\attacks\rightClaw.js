import { system, EntityDamageCause } from "@minecraft/server";
import { isEntityInFront } from "../constants_utils";
import { lightDamage } from "../index";
/**
 * Function to perform the right claw attack of Grimhowl.
 * @param {Entity} sourceEntity - The entity performing the attack.
 * @param {Entity[]} frontTargets - The targets in front of the source entity.
 */
export function doGrimhowlRightClaw(sourceEntity, frontTargets) {
    if (!sourceEntity)
        return;
    const isEnraged = sourceEntity.getProperty('ptd_dbb:enraged');
    const isSwordMode = sourceEntity.getProperty('ptd_dbb:sword_mode');
    const sourceLocation = sourceEntity.location;
    const defaultFacingLocation = sourceEntity.getViewDirection();
    if (isEnraged) {
        sourceEntity.triggerEvent('ptd_dbb:grimhowl_claw_right_enraged');
    }
    else {
        sourceEntity.triggerEvent('ptd_dbb:grimhowl_claw_right');
    }
    sourceEntity.teleport(sourceLocation, { facingLocation: frontTargets[0] ? frontTargets[0].location : defaultFacingLocation, keepVelocity: true });
    sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 5${1 + (Math.random() * 0.3)}`);
    system.runTimeout(() => {
        if (!sourceEntity)
            return;
        const updatedFrontTargets = frontTargets.filter((entity) => {
            return isEntityInFront(sourceEntity, entity) &&
                sourceEntity.dimension.getEntities({
                    location: sourceLocation,
                    excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
                    maxDistance: 10
                }).includes(entity);
        });
        updatedFrontTargets.forEach((entity) => {
            entity.applyDamage(lightDamage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
            entity.applyKnockback(sourceEntity.getViewDirection().x, sourceEntity.getViewDirection().z, isSwordMode ? 2.5 : -2.5, 0.25);
        });
    }, isEnraged ? 9 : 18);
    system.runTimeout(() => {
        if (!sourceEntity)
            return;
        sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.claw_attack @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
    }, isEnraged ? 0.75 * 10 : 0.75 * 20);
    system.runTimeout(() => {
        if (!sourceEntity)
            return;
        sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.pre_claw @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
    }, isEnraged ? 0.75 * 10 : 0.75 * 20);
}
