/**
 * Grimhowl boss spinning slash attack implementation.
 * This file contains the logic for executing the spinning slash attack,
 * which damages entities in a circular area around the boss.
 */
import { system, EntityDamageCause } from "@minecraft/server";
import { filterValidTargets } from "../constants_utils";
import { lightDamage, heavyDamage } from "../index";
/**
 * Executes the spinning slash attack for the Grimhowl boss
 * The attack damages entities in a circular area around the boss
 * and plays different animations based on boss state
 *
 * @param sourceEntity - The Grimhowl boss entity
 * @param nearbyTargets - Array of entities near the boss
 */
export function doGrimhowlSpinningSlash(sourceEntity, nearbyTargets) {
    const isEnraged = sourceEntity.getProperty('ptd_dbb:enraged');
    const isSwordMode = sourceEntity.getProperty('ptd_dbb:sword_mode');
    const sourceLocation = sourceEntity.location;
    if (isEnraged) {
        sourceEntity.triggerEvent('ptd_dbb:grimhowl_spinning_slash_enraged');
    }
    else {
        sourceEntity.triggerEvent('ptd_dbb:grimhowl_spinning_slash');
    }
    if (nearbyTargets.length === 0) {
        sourceEntity.triggerEvent('ptd_dbb:attack_done');
        return;
    }
    sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 5${1 + (Math.random() * 0.3)}`);
    system.runTimeout(() => {
        if (isSwordMode) {
            sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.sword_swipe @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
        }
        else {
            sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
            sourceEntity.runCommand(`playsound mob.ravager.bite @a ~ ~ ~ 10 ${2 + (Math.random() * 0.3)}`);
        }
        const viewDirection = sourceEntity.getViewDirection();
        const forwardLocation = {
            x: sourceLocation.x + viewDirection.x * -2.5,
            y: sourceLocation.y,
            z: sourceLocation.z + viewDirection.z * -2.5
        };
        const collideTargets = sourceEntity.dimension.getEntities({
            location: sourceLocation,
            excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
            maxDistance: 4.4
        }).filter(filterValidTargets(sourceEntity));
        const frontTargets = sourceEntity.dimension.getEntities({
            location: forwardLocation,
            excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
            maxDistance: 7.5
        }).filter(filterValidTargets(sourceEntity));
        collideTargets.forEach((entity) => {
            entity.applyDamage(lightDamage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
        });
        frontTargets.forEach((entity) => {
            entity.applyDamage(heavyDamage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
        });
    }, isEnraged ? 0.88 * 10 : 1 * 20);
    system.runTimeout(() => {
        if (isSwordMode) {
            sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.sword_swipe @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
        }
        else {
            sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
            sourceEntity.runCommand(`playsound mob.ravager.bite @a ~ ~ ~ 10 ${2 + (Math.random() * 0.3)}`);
        }
        const viewDirection = sourceEntity.getViewDirection();
        const forwardLocation = {
            x: sourceLocation.x + viewDirection.x * 2.5,
            y: sourceLocation.y,
            z: sourceLocation.z + viewDirection.z * 2.5
        };
        const collideTargets = sourceEntity.dimension.getEntities({
            location: sourceLocation,
            excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
            maxDistance: 4.4
        }).filter(filterValidTargets(sourceEntity));
        const frontTargets = sourceEntity.dimension.getEntities({
            location: forwardLocation,
            excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
            maxDistance: 7.5
        }).filter(filterValidTargets(sourceEntity));
        collideTargets.forEach((entity) => {
            entity.applyDamage(lightDamage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
        });
        frontTargets.forEach((entity) => {
            entity.applyDamage(heavyDamage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
        });
    }, isEnraged ? 1.88 * 10 : 2 * 20);
}
