import { EntityComponentTypes, GameMode, Player, system } from "@minecraft/server";
import { getDistance } from "../../../utilities/vector3";
/**
 * Executes the healing ability for the Piglin Champion
 * Heals the piglin and applies knockback to nearby entities during the roar
 *
 * @param piglinChampion The piglin champion entity
 * @param phase The phase of the healing ability (1 for healing, 2 for knockback)
 */
export function executeHealingAbility(piglinChampion, phase) {
    // Phase 1: Start progressive healing between ticks 43 and 88
    if (phase === 1) {
        // Get health component
        const healthComponent = piglinChampion.getComponent(EntityComponentTypes.Health);
        const maxHealth = healthComponent?.defaultValue || 0; // Default max health if not set
        // Calculate total heal amount (10% of max health)
        const totalHealAmount = Math.ceil(maxHealth * 0.1);
        // Start progressive healing
        startProgressiveHealing(piglinChampion, totalHealAmount);
    }
    // Phase 2: Start continuous knockback from tick 130 (roar) until the end of healing sequence
    else if (phase === 2) {
        // Start continuous knockback
        startContinuousKnockback(piglin<PERSON>hampion);
    }
}
/**
 * Applies continuous knockback to nearby entities from the roar event until the healing sequence ends
 *
 * @param piglinChampion The piglin champion entity
 */
async function startContinuousKnockback(piglinChampion) {
    // Get the current attack timer
    let attackTimer = piglinChampion.getProperty("ptd_dbb:attack_timer");
    // Wait until tick 130 before starting the continuous knockback
    while (attackTimer < 130) {
        await system.waitTicks(1);
        try {
            // Update attack timer
            attackTimer = piglinChampion.getProperty("ptd_dbb:attack_timer");
        }
        catch (e) {
            // Entity might have been removed
            return;
        }
    }
    // Calculate the duration of continuous knockback (from tick 130 to tick 145)
    const knockbackDuration = 145 - 130; // 15 ticks of knockback
    const knockbackRadius = 8;
    // Apply knockback every tick for the duration
    for (let i = 0; i < knockbackDuration; i++) {
        try {
            // Use the piglin's location as the origin for the knockback
            const originPos = piglinChampion.location;
            // Apply knockback to all entities within the radius
            piglinChampion.dimension.getEntities({
                location: originPos,
                maxDistance: knockbackRadius,
                excludeFamilies: ["piglin_champion", "piglin", "rock"]
            }).forEach((entity) => {
                // Use piglin's direction for knockback
                // Create 2D points (same y-coordinate) to calculate horizontal distance
                const point1 = { x: entity.location.x, y: 0, z: entity.location.z };
                const point2 = { x: originPos.x, y: 0, z: originPos.z };
                const distance = getDistance(point1, point2);
                if (distance > 0) {
                    // Calculate direction from piglin to entity
                    const dx = entity.location.x - originPos.x;
                    const dz = entity.location.z - originPos.z;
                    // Normalize direction
                    const length = Math.sqrt(dx * dx + dz * dz);
                    const nx = dx / length;
                    const nz = dz / length;
                    // Use slightly reduced knockback strength for continuous knockback
                    // to prevent players from being pushed too far away
                    const horizontalStrength = 3.5; // Half the strength of the initial roar
                    const verticalStrength = 0.4;
                    try {
                        // Try to apply knockback first
                        if (entity instanceof Player) {
                            const gameMode = entity.getGameMode();
                            if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                                entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                            }
                        }
                        else {
                            entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                        }
                    }
                    catch (e) {
                        // Fallback to applyImpulse if applyKnockback fails
                        const impulse = {
                            x: nx * horizontalStrength,
                            y: verticalStrength,
                            z: nz * horizontalStrength
                        };
                        entity.applyImpulse(impulse);
                    }
                }
            });
            // Wait for next tick
            await system.waitTicks(1);
            // Check if the entity is still valid and still in healing state
            const attack = piglinChampion.getProperty("ptd_dbb:attack");
            if (attack !== "healing") {
                return; // Stop knockback if the attack state changed
            }
        }
        catch (e) {
            // Entity might have been removed
            return;
        }
    }
}
/**
 * Applies healing progressively over time between ticks 43 and 88
 *
 * @param piglinChampion The piglin champion entity
 * @param totalHealAmount The total amount to heal
 */
async function startProgressiveHealing(piglinChampion, totalHealAmount) {
    // Get the current attack timer
    let attackTimer = piglinChampion.getProperty("ptd_dbb:attack_timer");
    // Wait until tick 43 before starting the progressive healing
    while (attackTimer < 43) {
        await system.waitTicks(1);
        try {
            // Update attack timer
            attackTimer = piglinChampion.getProperty("ptd_dbb:attack_timer");
        }
        catch (e) {
            // Entity might have been removed
            return;
        }
    }
    // Calculate healing parameters
    const healingDuration = 88 - 43; // 45 ticks of healing
    const healPerTick = totalHealAmount / healingDuration;
    // Apply healing progressively
    for (let i = 0; i < healingDuration; i++) {
        try {
            // Get health component
            const healthComponent = piglinChampion.getComponent(EntityComponentTypes.Health);
            if (!healthComponent)
                return;
            // Get current health
            const currentHealth = healthComponent.currentValue;
            const maxHealth = healthComponent.defaultValue;
            // Calculate new health with progressive healing
            const newHealth = Math.min(currentHealth + healPerTick, maxHealth);
            // Apply healing
            healthComponent.setCurrentValue(newHealth);
            // Visual feedback for healing (optional)
            piglinChampion.dimension.spawnParticle("minecraft:heart_particle", {
                x: piglinChampion.location.x,
                y: piglinChampion.location.y + 2,
                z: piglinChampion.location.z
            });
            // Wait for next tick
            await system.waitTicks(1);
            // Check if the entity is still valid and still in healing state
            const attack = piglinChampion.getProperty("ptd_dbb:attack");
            if (attack !== "healing") {
                return; // Stop healing if the attack state changed
            }
        }
        catch (e) {
            // Entity might have been removed
            return;
        }
    }
}
