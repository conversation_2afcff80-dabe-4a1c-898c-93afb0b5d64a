import { spawnEntitiesWithInterval } from "../../../utilities/summonEntity";
import { getRandomLocation } from "../../../utilities/vector3";
import { getTarget } from "../../general_mechanics/targetUtils";
/**
 * Configuration for the soul hands attack
 */
const SOUL_HANDS_CONFIG = {
    /** Total attack duration in ticks */
    ATTACK_DURATION: 90,
    /** Minimum number of minions to summon */
    MIN_MINION_COUNT: 3,
    /** Maximum number of minions to summon */
    MAX_MINION_COUNT: 5
};
/**
 * Executes the soul hands attack for the Necromancer
 * Summons skeleton souls around the target
 * @param necromancer The necromancer entity
 * @returns Promise<void>
 */
export async function executeSoulHandsAttack(necromancer) {
    try {
        // Get the target
        const target = getTarget(necromancer, necromancer.location, 32, ["necromancer"]);
        // If no target, abort the attack
        if (!target) {
            return;
        }
        // Generate a random count for skeleton souls to summon
        const skeletonSoulCount = Math.floor(SOUL_HANDS_CONFIG.MIN_MINION_COUNT +
            Math.random() * (SOUL_HANDS_CONFIG.MAX_MINION_COUNT - SOUL_HANDS_CONFIG.MIN_MINION_COUNT + 1));
        const entityConfigs = [
            {
                entityId: "ptd_dbb:skeleton_soul",
                count: skeletonSoulCount
            }
        ];
        // Spawn the minions with a delay between each
        await spawnEntitiesWithInterval(necromancer.dimension, entityConfigs, () => {
            // Get a random position around the TARGET
            const pos = getRandomLocation(target.location, necromancer.dimension, 3, // Base offset (minimum distance from target)
            5, // Additional random offset
            0, // No Y offset
            true // Check for air block
            );
            // Add visual effects if position is valid
            if (pos) {
                necromancer.dimension.spawnParticle("minecraft:soul_particle", pos);
                necromancer.dimension.playSound("mob.ptd_dbb_necromancer.soul_hands", pos);
            }
            return pos;
        }, 1, // Delay between spawns
        (entity) => {
            // Play sound effect when entity is spawned
            necromancer.dimension.playSound("mob.skeleton.say", entity.location);
        });
    }
    catch (error) {
        console.warn(`Error in soul hands attack: ${error}`);
    }
}
