import { applyDeathCameraShake, handleDeathMechanics } from "../general_mechanics/deathMechanics";
import { stopNecromancerSounds } from "./soundManager";
import { spawnItemFountain } from "../general_mechanics/itemFountain";
import { handleAttackLogic, selectAttack } from "./controller";
import { getTarget } from "../general_mechanics/targetUtils";
/**
 * Handles the necromancer boss mechanics
 * Currently implements spawning, idle, and movement mechanics
 *
 * @param necromancer The necromancer entity
 */
export function necromancerMechanics(necromancer) {
    try {
        // Skip if entity is not valid
        if (!necromancer)
            return;
        // Handle death mechanics using the generalized function
        // If the entity is dead, this will handle all death-related behavior and return true
        if (handleDeathMechanics(necromancer, {
            // Configure death mechanics specific to the Necromancer
            duration: 150,
            xpOrbs: {
                count: 8,
                duration: 100,
                heightOffset: 2.25
            },
            // No drops here as we'll use a custom event to spawn the essence fountain
            drops: [],
            deathSound: "mob.ptd_dbb_necromancer.death",
            // Add custom event to spawn essence fountain at the beginning of death sequence
            customEvents: [
                {
                    tick: 1,
                    callback: (entity) => {
                        applyDeathCameraShake(necromancer, 150);
                        // Spawn 32 essence items in a fountain-like effect
                        spawnItemFountain(entity, "ptd_dbb:necromancer_essence", 32, {
                            heightOffset: 2.25,
                            particleEffect: "minecraft:large_explosion",
                            soundEffect: "random.pop",
                            minVerticalStrength: 0.1,
                            maxVerticalStrength: 0.3,
                            minHorizontalStrength: 0.05,
                            maxHorizontalStrength: 0.2
                        });
                    }
                }
            ],
            // Provide the sound stopping function
            stopSoundsFn: (entity, excludedSound) => stopNecromancerSounds(entity, undefined, excludedSound)
        })) {
            // If death mechanics were applied, return early
            return;
        }
        // Handle spawning animation
        const isSpawning = necromancer.getProperty("ptd_dbb:spawning");
        if (isSpawning) {
            // Get current spawning ticks
            let spawningTicks = necromancer.getProperty("ptd_dbb:spawning_ticks");
            // Increment spawning ticks
            spawningTicks = Math.min(spawningTicks + 1, 107); // 107 ticks for spawning animation
            necromancer.setProperty("ptd_dbb:spawning_ticks", spawningTicks);
            // Apply effects during spawning
            if (spawningTicks === 1) {
                // Play spawn sound at the beginning of the animation
                // Commented out as requested to skip sound effects for now
                // necromancer.dimension.playSound("mob.ptd_dbb_necromancer.spawn", necromancer.location);
            }
            // The necromancer is a floating entity, so we don't need any special effects at the end of spawning
            if (spawningTicks === 107) {
                // Spawn particles for visual effect
                necromancer.dimension.spawnParticle("minecraft:large_explosion", necromancer.location);
            }
            return; // Exit early during spawning
        }
        // Handle attack mechanics
        const attack = necromancer.getProperty("ptd_dbb:attack");
        let attackTimer = necromancer.getProperty("ptd_dbb:attack_timer");
        let attackCooldown = necromancer.getProperty("ptd_dbb:attack_cooldown");
        // Find a target for attacks using the getTarget function
        const target = getTarget(necromancer, necromancer.location, 32, ["necromancer"]);
        if (target) {
            // Handle attack cooldown
            if (attackCooldown > 0) {
                // Use Math.max to ensure cooldown never goes below 0
                necromancer.setProperty("ptd_dbb:attack_cooldown", Math.max(0, attackCooldown - 1));
            }
            // If we have a target, not currently attacking, and cooldown is 0, select an attack
            if (attack === "none" && attackCooldown === 0) {
                selectAttack(necromancer, target);
            }
        }
        // Handle attack timer and logic if already in an attack
        if (attack !== "none") {
            // Increment attack timer with a cap to prevent out-of-range errors
            const maxDuration = 200; // Default to 10 seconds if not defined
            attackTimer = Math.min(attackTimer + 1, maxDuration);
            necromancer.setProperty("ptd_dbb:attack_timer", attackTimer);
            // Handle attack logic based on timer
            handleAttackLogic(necromancer, attack, attackTimer);
        }
    }
    catch (error) {
        console.warn(`Error in necromancer mechanics: ${error}`);
    }
}
