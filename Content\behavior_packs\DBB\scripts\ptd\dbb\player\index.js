import { world, InputPermissionCategory } from "@minecraft/server";
/**
 * Restores all input permissions for a player
 * This is particularly useful when a player rejoins the game after leaving while under a stun effect
 *
 * @param player The player to restore permissions for
 */
function restorePlayerInputPermissions(player) {
    try {
        // Restore movement permission
        player.inputPermissions.setPermissionCategory(InputPermissionCategory.Movement, true);
        // Restore camera permission (in case it was also disabled)
        player.inputPermissions.setPermissionCategory(InputPermissionCategory.Camera, true);
        console.log(`Restored input permissions for player: ${player.name}`);
    }
    catch (error) {
        console.warn(`Failed to restore input permissions for player: ${player.name}. Error: ${error}`);
    }
}
/**
 * Handler for player spawn events
 * Restores input permissions when a player joins or respawns
 *
 * @param event The player spawn event
 */
function handlePlayerSpawn(event) {
    // Restore input permissions for the spawned player
    restorePlayerInputPermissions(event.player);
}
// Subscribe to the playerSpawn event
world.afterEvents.playerSpawn.subscribe(handlePlayerSpawn);
// Export the function so it can be used elsewhere if needed
export { restorePlayerInputPermissions };
