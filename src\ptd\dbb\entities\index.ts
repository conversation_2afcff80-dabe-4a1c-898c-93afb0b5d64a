import { Entity, EntityDamageSource, system, world } from "@minecraft/server";
import { handleLootMechanics } from "./lootMechanics";
import { zombieBruteMechanics } from "../bosses/necromancer/minions/zombie_brute/index";
import { grimhowlMechanics } from "../bosses/grimhowl/index";

export const customEntityIds = new Set<string>(["ptd_dbb:winged_zombie", "ptd_dbb:zombie_brute", "ptd_dbb:skeleton_soul"]);

// Global entity event listeners
world.afterEvents.entityHurt.subscribe((event: { damage: number; damageSource: EntityDamageSource; hurtEntity: Entity }) => {
  if (event.hurtEntity.typeId !== "ptd_dbb:grimhowl") return;
  grimhowlMechanics(event, "hurt");
});

world.afterEvents.entityLoad.subscribe((event: { entity: Entity }) => {
  if (event.entity.typeId !== "ptd_dbb:grimhowl") return;
  grimhowlMechanics(event, "load");
});

world.afterEvents.dataDrivenEntityTrigger.subscribe(async (event) => {
  const entity = event.entity;
  const entityTypeId = entity.typeId;
  const eventId = event.eventId;

  // Wait 1 tick so the selected attack is set properly
  await system.waitTicks(1);

  // Handle attack logic
  if (eventId === "ptd_dbb:attack") {
    switch (entityTypeId) {
      case "ptd_dbb:zombie_brute":
        zombieBruteMechanics(entity);
        break;
      default:
        break;
    }
  }

  // Handle death logic
  if (eventId === "ptd_dbb:dead") {
    switch (entityTypeId) {
      case "ptd_dbb:zombie_brute":
        deathMechanics(entity, 36);
        break;
      default:
        break;
    }
  }
});

function deathMechanics(entity: Entity, ticks: number) {
  if (!entity) return;

  const deathProp = entity.getProperty("ptd_dbb:dead") as boolean;

  if (deathProp) {
    let currentTick = 0;

    // Create an interval that runs death mechanics every tick for the specified duration
    const deathInterval = system.runInterval(() => {
      try {
        // Check if entity is still valid
        if (!entity) {
          system.clearRun(deathInterval);
          return;
        }

        // Execute death mechanics for this tick
        handleLootMechanics(entity);

        currentTick++;

        // Clear interval when we've reached the specified duration
        if (currentTick >= ticks) {
          system.clearRun(deathInterval);
        }
      } catch (error) {
        // Clear interval on any error to prevent memory leaks
        system.clearRun(deathInterval);
        console.warn(`Error in death mechanics interval: ${error}`);
      }
    }, 1); // Run every tick
  }
  return;
}
