import { filterValidTargets, isEntityInFront } from "./constants_utils";
import { getEntityWeights, selectEvent, updateWeights } from "./attack_controller";
import { doGrimhowlLeftClaw, doGrimhowlRightClaw, doGrimhowlSpinningSlash, doGrimhowlBackstep } from "./attacks/index";
/**
 * Handles melee range attacks for the Grimhowl boss
 * Selects and executes appropriate attacks based on target positions
 *
 * @param sourceEntity - The Grimhowl boss entity
 */
export function grimhowlMeleeRangeAttackHandler(sourceEntity) {
    if (!sourceEntity)
        return;
    const nearbyTargets = sourceEntity.dimension.getEntities({
        location: sourceEntity.location,
        excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
        maxDistance: 10
    }).filter(filterValidTargets(sourceEntity));
    const frontTargets = nearbyTargets.filter((entity) => isEntityInFront(sourceEntity, entity));
    if (nearbyTargets.length > 0) {
        const entityId = sourceEntity.id;
        const weights = getEntityWeights(entityId);
        let selectedEvent = selectEvent(weights);
        if ((selectedEvent === "clawLeft" || selectedEvent === "clawRight") && frontTargets.length === 0) {
            selectedEvent = "spinningSlash";
        }
        if ((selectedEvent === "clawLeft" || selectedEvent === "clawRight") && frontTargets.length > 0) {
            if (selectedEvent === "clawLeft") {
                doGrimhowlLeftClaw(sourceEntity, frontTargets);
            }
            else {
                doGrimhowlRightClaw(sourceEntity, frontTargets);
            }
        }
        else if (selectedEvent === "spinningSlash") {
            doGrimhowlSpinningSlash(sourceEntity, nearbyTargets);
        }
        else if (selectedEvent === "backstep") {
            const targetLocation = frontTargets[0] || nearbyTargets[0];
            if (!targetLocation) {
                sourceEntity.triggerEvent('ptd_dbb:attack_done');
                return; // Finish attack if no target and return early
            }
            doGrimhowlBackstep(sourceEntity, targetLocation);
        }
        updateWeights(entityId, selectedEvent);
    }
    else {
        sourceEntity.triggerEvent('ptd_dbb:attack_done');
        return;
    }
}
