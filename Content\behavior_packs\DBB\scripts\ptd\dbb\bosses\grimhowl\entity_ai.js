/**
 * Grimhowl boss AI behavior and event handling.
 * This file contains the core logic for handling script events, hurt events,
 * and managing the boss's state (enraged mode, damage tracking, etc.).
 */
import { system } from "@minecraft/server";
import { doGrimhowlRoar, doGrimhowlSlash, doGrimhowlPounce, doGrimhowlShadowOnslaught } from "./attacks/index";
import { grimhowlMeleeRangeAttackHandler } from "./attack_handler";
/**
 * Map to track cumulative damage taken by each Grimhowl boss instance
 * Key: Entity ID, Value: Total damage taken
 */
export const grimHowlDamageTracker = new Map();
/**
 * Handles script events for the Grimhowl boss
 * Routes different script events to appropriate handler functions
 *
 * @param event - The script event object
 * @param event.id - The ID of the script event
 * @param event.sourceEntity - The entity that triggered the event
 */
export function grimhowlScriptEventHandler(event) {
    try {
        const sourceEntity = event.sourceEntity;
        switch (event.id) {
            case "ptd_dbb:run_melee_range_attack":
                grimhowlMeleeRangeAttackHandler(sourceEntity);
                break;
            case "ptd_dbb:run_mid_range_attack":
                doGrimhowlSlash(sourceEntity);
                break;
            case "ptd_dbb:run_long_range_attack":
                if (sourceEntity.getProperty('ptd_dbb:enraged')) {
                    doGrimhowlShadowOnslaught(sourceEntity);
                }
                else {
                    doGrimhowlPounce(sourceEntity);
                }
                break;
            case "ptd_dbb:enrage_toggle":
                toggleEnrageMode(sourceEntity);
                break;
            case "ptd_dbb:transition_to_swordless":
                sourceEntity.triggerEvent('ptd_dbb:transition_to_swordless');
                break;
            case "ptd_dbb:grimhowl_do_the_roar":
                if (sourceEntity.getProperty('ptd_dbb:enraged')) {
                    sourceEntity.triggerEvent('ptd_dbb:attack_done');
                    doGrimhowlRoar(sourceEntity);
                }
                else {
                    sourceEntity.triggerEvent('ptd_dbb:attack_done');
                    sourceEntity.setProperty('ptd_dbb:enraged', true);
                    doGrimhowlRoar(sourceEntity);
                }
                break;
            case "ptd_dbb:grimhowl_arrows_reset":
                let arrowHits = 0;
                for (let i = 1; i <= 12; i++) {
                    sourceEntity.setProperty(`ptd_dbb:arrow_hit_${i}`, false);
                    arrowHits++;
                }
                break;
            default:
                break;
        }
    }
    catch (error) {
        event.sourceEntity.triggerEvent('ptd_dbb:attack_done');
    }
}
/**
 * Handles hurt events for the Grimhowl boss
 * Manages arrow hit detection, sword mode transition, and damage tracking
 *
 * @param event - The hurt event object containing damage and entity information
 */
export function grimhowlHurtHandler(event) {
    const target = event.hurtEntity;
    if (target && target.typeId === "ptd_dbb:grimhowl") {
        const damagingProjectile = event.damageSource.damagingProjectile;
        target.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 10 ${0.5 + (Math.random() * 0.3)}`);
        const damage = event.damage;
        // --- Arrow hit logic start ---
        if (damagingProjectile && event.damage >= 4.5) {
            const arrowProps = [];
            for (let i = 1; i <= 12; i++) {
                arrowProps.push(`ptd_dbb:arrow_hit_${i}`);
            }
            const available = arrowProps.filter(prop => !target.getProperty(prop));
            if (available.length > 0) {
                const propName = available[Math.floor(Math.random() * available.length)];
                target.setProperty(propName, true);
            }
        }
        // --- Arrow hit logic end ---
        const healthComponent = target.getComponent('minecraft:health');
        const healthPercentage = healthComponent.currentValue / healthComponent.effectiveMax;
        if (healthPercentage <= 0.5 && !target.getProperty('ptd_dbb:sword_mode')) {
            target.triggerEvent('ptd_dbb:transition_to_sword');
            return;
        }
        handleGrimhowlDamage(target, damage);
    }
}
/**
 * Handles load events for the Grimhowl boss
 * Initializes boss state when loaded
 *
 * @param event - The load event object containing the entity information
 */
export function grimhowlLoadHandler(event) {
    const target = event.entity;
    if (target && target.typeId === "ptd_dbb:grimhowl") {
        if (target.getProperty('ptd_dbb:enraged')) {
            toggleEnrageMode(target);
        }
        if (target.getDynamicProperty('ptd_dbb:boss_camera_ongoing')) {
            target.setDynamicProperty('ptd_dbb:boss_camera_ongoing', false);
        }
    }
}
/**
 * Toggles the enraged mode for the Grimhowl boss
 * When entering enraged mode, triggers a roar attack
 *
 * @param sourceEntity - The Grimhowl boss entity
 */
export function toggleEnrageMode(sourceEntity) {
    if (sourceEntity.getProperty('ptd_dbb:enraged')) {
        sourceEntity.setProperty('ptd_dbb:enraged', false);
    }
    else {
        sourceEntity.triggerEvent('ptd_dbb:attack_done');
        sourceEntity.setProperty('ptd_dbb:enraged', true);
        doGrimhowlRoar(sourceEntity);
    }
}
/**
 * Handles damage tracking for the Grimhowl boss
 * Accumulates damage and triggers enrage mode when threshold is reached
 * Also handles arrow hit shake animation when arrows are stuck in the boss
 *
 * @param target - The Grimhowl boss entity
 * @param damage - The amount of damage taken
 */
export function handleGrimhowlDamage(target, damage) {
    const targetId = target.id;
    if (!grimHowlDamageTracker.has(targetId)) {
        grimHowlDamageTracker.set(targetId, 0);
    }
    const totalDamage = (grimHowlDamageTracker.get(targetId) ?? 0) + damage;
    grimHowlDamageTracker.set(targetId, totalDamage);
    if (totalDamage >= 100) {
        let arrowHits = 0;
        for (let i = 1; i <= 12; i++) {
            if (target.getProperty(`ptd_dbb:arrow_hit_${i}`))
                arrowHits++;
        }
        if (arrowHits > 0) {
            target.triggerEvent('ptd_dbb:grimhowl_arrow_shake');
            target.runCommand(`playsound mob.ptd_dbb_grimhowl.arrow_shake @a ~ ~ ~ 10 ${0.5 + (Math.random() * 0.3)}`);
            return;
        }
        else {
            toggleEnrageMode(target);
        }
        grimHowlDamageTracker.set(targetId, 0);
        if (!target.getProperty('ptd_dbb:enraged')) {
            system.runTimeout(() => {
                if (target) {
                    toggleEnrageMode(target);
                }
                else {
                    return;
                }
            }, 20 * 20);
        }
    }
}
