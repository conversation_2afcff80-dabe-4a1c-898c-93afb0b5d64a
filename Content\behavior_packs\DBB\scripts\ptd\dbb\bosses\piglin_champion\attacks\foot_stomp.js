import { Entity<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Game<PERSON><PERSON>, Player } from "@minecraft/server";
import { getDistance } from "../../../utilities/vector3";
import { PIGLIN_CHAMPION_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
/**
 * Executes the foot stomp attack for the Piglin Champion
 * Applies damage and knockback to nearby entities in all directions
 *
 * @param piglinChampion The piglin champion entity
 */
export function executeFootStompAttack(piglinChampion) {
    // Apply damage to nearby entities
    const damageRadius = 5;
    // Use direct damage value instead of percentage
    const damage = PIGLIN_CHAMPION_ATTACK_DAMAGES.foot_stomp.damage;
    // Get direction vector directly from the piglin's view direction
    const viewDirection = piglinChampion.getViewDirection();
    const dirX = viewDirection.x;
    const dirZ = viewDirection.z;
    // Calculate position 3 blocks in front of the piglin as the origin for the attack
    const originPos = {
        x: piglinChampion.location.x + dirX * 3,
        y: piglin<PERSON>hampion.location.y,
        z: piglinChampion.location.z + dirZ * 3
    };
    // Apply knockback to all entities within the radius, but only damage non-items/XP orbs
    piglinChampion.dimension
        .getEntities({
        location: originPos,
        maxDistance: damageRadius,
        excludeFamilies: ["piglin_champion", "piglin", "rock"]
    })
        .forEach((entity) => {
        // Apply damage only to entities that are not XP orbs or items
        if (entity.typeId !== "minecraft:xp_orb" && entity.typeId !== "minecraft:item") {
            entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinChampion });
        }
        // Calculate direction from piglin to entity for knockback
        const dx = entity.location.x - originPos.x;
        const dz = entity.location.z - originPos.z;
        // Calculate distance for normalization
        const distance = Math.sqrt(dx * dx + dz * dz);
        if (distance > 0) {
            // Normalize the direction vector
            const nx = dx / distance;
            const nz = dz / distance;
            // Foot stomp attack parameters
            const horizontalStrength = 3.0; // 3 blocks of knockback
            const verticalStrength = 0.5;
            try {
                // Try to apply knockback first
                if (entity instanceof Player) {
                    const gameMode = entity.getGameMode();
                    if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                        entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                    }
                }
                else {
                    entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                }
            }
            catch (e) {
                // Fallback to applyImpulse if applyKnockback fails
                const impulse = {
                    x: nx * horizontalStrength,
                    y: verticalStrength,
                    z: nz * horizontalStrength
                };
                entity.applyImpulse(impulse);
            }
        }
    });
    // Apply camera shake to nearby players
    applyCameraShake(piglinChampion);
}
/**
 * Applies camera shake to players within a radius of the entity
 * @param source The entity causing the camera shake
 */
function applyCameraShake(source) {
    const radius = 32; // 32 block radius for camera shake
    const minStrength = 0.02;
    const maxStrength = 0.5;
    const length = 0.5;
    const sourceLocation = source.location;
    const dimension = source.dimension;
    // Get all players within the radius
    const players = dimension.getPlayers({
        location: sourceLocation,
        maxDistance: radius
    });
    // Apply camera shake to each player
    players.forEach((player) => {
        // Only apply to players on the ground
        if (!player.isOnGround)
            return;
        // Calculate distance from source
        const distance = getDistance(sourceLocation, player.location);
        // Skip if outside radius (should be handled by getPlayers, but just in case)
        if (distance > radius)
            return;
        // Calculate strength based on distance (inversely proportional)
        // Players closer to the source get stronger shake
        const strengthMultiplier = 1 - distance / radius; // 0 at edge, 1 at center
        const strength = minStrength + strengthMultiplier * (maxStrength - minStrength);
        // Apply camera shake using /camerashake command with player's name
        player.runCommand(`camerashake add @s ${strength.toFixed(2)} ${length.toFixed(2)} positional`);
    });
}
