import { EntityDamageCause, InputPermission<PERSON><PERSON><PERSON><PERSON>, Player } from "@minecraft/server";
import { system } from "@minecraft/server";
import { NECROMANCER_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
/**
 * Configuration for the soul drain attack
 */
const SOUL_DRAIN_CONFIG = {
    /** Duration for which player input permissions are disabled */
    STUN_DURATION: 79
};
/**
 * Executes the soul drain attack for the Necromancer
 * Spawns a soul trap entity at the target's location and disables player movement
 * Deals 4 damage per tick to the target for the duration of the attack
 * For non-player entities, teleports them back to the initial location
 * For player entities, the soul trap follows the player
 *
 * @param necromancer The necromancer entity
 * @param target The target entity
 * @returns void
 */
export async function executeSoulDrainAttack(necromancer, target) {
    try {
        // Save the initial location of the target when the soul trap is spawned
        const initialLocation = { ...target.location };
        // Spawn the soul trap entity at the target's location
        const soulTrap = necromancer.dimension.spawnEntity("ptd_dbb:soul_trap", initialLocation);
        // Set up position check and teleport functionality
        let positionCheckId;
        // Start a periodic check to handle entity positions
        positionCheckId = system.runInterval(() => {
            try {
                // Check if both the target and soul trap are still valid
                if (target && target.dimension && soulTrap && soulTrap.dimension) {
                    // Use direct damage value instead of percentage
                    const damage = NECROMANCER_ATTACK_DAMAGES.soul_drain.damage;
                    target.applyDamage(damage, { cause: EntityDamageCause.entityAttack });
                    if (target instanceof Player) {
                        // For players: Update the soul trap position to match the player's current position
                        soulTrap.teleport(target.location);
                    }
                    else {
                        // For non-player entities: Teleport them back to the initial location
                        target.teleport(initialLocation);
                    }
                }
                else {
                    // Either target or soul trap is no longer valid, clear the interval
                    if (positionCheckId !== undefined) {
                        system.clearRun(positionCheckId);
                        positionCheckId = undefined;
                    }
                }
            }
            catch (error) {
                // Clear the interval on error
                if (positionCheckId !== undefined) {
                    system.clearRun(positionCheckId);
                    positionCheckId = undefined;
                }
            }
        });
        // If the target is a player, disable their movement permissions
        if (target instanceof Player) {
            // Disable movement but allow camera movement
            target.inputPermissions.setPermissionCategory(InputPermissionCategory.Movement, false);
        }
        // Schedule restoration of permissions and cleanup after the stun duration
        let timeout = system.runTimeout(() => {
            try {
                // Clear the position check interval
                if (positionCheckId !== undefined) {
                    system.clearRun(positionCheckId);
                    positionCheckId = undefined;
                }
                // Check if the target is still valid
                if (target && target.dimension) {
                    // Restore movement permissions if target is a player
                    if (target instanceof Player) {
                        target.inputPermissions.setPermissionCategory(InputPermissionCategory.Movement, true);
                    }
                }
                system.clearRun(timeout);
            }
            catch (error) {
                // handle error silently
            }
        }, SOUL_DRAIN_CONFIG.STUN_DURATION);
    }
    catch (error) {
        console.warn(`Error in soul drain attack: ${error}`);
    }
}
