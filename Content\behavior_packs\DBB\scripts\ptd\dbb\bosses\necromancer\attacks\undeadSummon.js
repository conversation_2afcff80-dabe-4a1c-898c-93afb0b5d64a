import { spawnEntitiesWithInterval } from "../../../utilities/summonEntity";
import { getRandomLocation } from "../../../utilities/vector3";
/**
 * Configuration for the undead summon attack
 */
const UNDEAD_SUMMON_CONFIG = {
    /** Total attack duration in ticks */
    ATTACK_DURATION: 115,
    /** Summoning duration in ticks */
    SUMMON_DURATION: 40,
    /** Number of minions to summon */
    MINION_COUNT: 3
};
/**
 * Executes the undead summon attack for the Necromancer
 * Summons winged and brute zombies randomly around the necromancer
 * @param necromancer The necromancer entity
 * @returns Promise<void>
 */
export async function executeUndeadSummonAttack(necromancer) {
    try {
        // Configure the entities to spawn randomly
        // Generate a random count for winged zombies
        const wingedZombieCount = Math.floor(Math.random() * UNDEAD_SUMMON_CONFIG.MINION_COUNT);
        // The zombie brute count is the remaining minions to reach the total
        const zombieBruteCount = UNDEAD_SUMMON_CONFIG.MINION_COUNT - wingedZombieCount;
        const entityConfigs = [
            {
                entityId: "ptd_dbb:winged_zombie",
                count: wingedZombieCount
            },
            {
                entityId: "ptd_dbb:zombie_brute",
                count: zombieBruteCount
            }
        ].filter((config) => config.count > 0);
        // Spawn the minions with a delay between each
        await spawnEntitiesWithInterval(necromancer.dimension, entityConfigs, () => {
            // Get a random position around the necromancer
            const pos = getRandomLocation(necromancer.location, necromancer.dimension, 3, // Base offset (minimum distance from necromancer)
            4, // Additional random offset
            0, // No Y offset
            true // Check for air block
            );
            // Add visual effects if position is valid
            if (pos) {
                necromancer.dimension.spawnParticle("minecraft:large_explosion", pos);
            }
            return pos;
        }, Math.floor(UNDEAD_SUMMON_CONFIG.SUMMON_DURATION / UNDEAD_SUMMON_CONFIG.MINION_COUNT), // Delay between spawns
        (entity) => {
            // Play sound effect when entity is spawned
            necromancer.dimension.playSound("mob.zombie.spawn", entity.location);
        });
    }
    catch (error) {
        console.warn(`Error in undead summon attack: ${error}`);
    }
}
