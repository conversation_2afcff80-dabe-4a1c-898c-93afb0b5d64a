import { system } from "@minecraft/server";
import { executeSplashAttack } from "./attacks/splash";
/**
 * When the attack should apply in the animation
 */
const ATTACK_TIMING = 35; // Apply damage at tick 35
/**
 * The total animation time in ticks
 */
const ANIMATION_TIME = 88; // Total animation time in ticks
/**
 * Cooldown before executing the next attack
 */
const COOLDOWN_TIME = 20;
/**
 * Handles all mechanics for the Zombie Brute
 * This includes attack selection, attack execution, and cooldown management
 *
 * @param zombieBrute The zombie brute entity
 */
export async function zombieBruteMechanics(zombieBrute) {
    // Skip if entity is not valid
    try {
        if (!zombieBrute)
            return;
        // Skip if entity is spawning or dead
        const isSpawning = zombieBrute.getProperty("ptd_dbb:spawning");
        const isDead = zombieBrute.getProperty("ptd_dbb:dead");
        if (isSpawning || isDead)
            return;
        // Apply slowness effect to prevent movement during the attack
        zombieBrute.addEffect("minecraft:slowness", ANIMATION_TIME, { amplifier: 250, showParticles: false });
        // Wait for attack timing, then execute splash attack
        await system.waitTicks(ATTACK_TIMING);
        executeSplashAttack(zombieBrute);
        // Wait for remaining animation time, then reset attack property and set cooldown
        await system.waitTicks(ANIMATION_TIME - ATTACK_TIMING);
        zombieBrute.triggerEvent("ptd_dbb:reset_attack");
        // Wait for cooldown, then set cooldown property to false
        await system.waitTicks(COOLDOWN_TIME);
        zombieBrute.setProperty("ptd_dbb:cooling_down", false);
    }
    catch (e) {
        return;
    }
}
